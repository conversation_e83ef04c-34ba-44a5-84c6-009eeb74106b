[{"/var/www/html/src/app/admin/AdminPageContent.tsx": "1", "/var/www/html/src/app/admin/page.tsx": "2", "/var/www/html/src/app/api/cabinets/[id]/route.ts": "3", "/var/www/html/src/app/api/cabinets/route.ts": "4", "/var/www/html/src/app/api/categories/route.ts": "5", "/var/www/html/src/app/api/footer/route.ts": "6", "/var/www/html/src/app/api/hero/route.ts": "7", "/var/www/html/src/app/api/kitchens/[id]/route.ts": "8", "/var/www/html/src/app/api/kitchens/route.ts": "9", "/var/www/html/src/app/api/uploads/route.ts": "10", "/var/www/html/src/app/api/why-choose-us/route.ts": "11", "/var/www/html/src/app/cabinets/CabinetsPageContent.tsx": "12", "/var/www/html/src/app/cabinets/page.tsx": "13", "/var/www/html/src/app/kitchens/KitchensPageContent.tsx": "14", "/var/www/html/src/app/kitchens/page.tsx": "15", "/var/www/html/src/app/layout.tsx": "16", "/var/www/html/src/app/manifest.ts": "17", "/var/www/html/src/app/page.tsx": "18", "/var/www/html/src/app/sitemap.ts": "19", "/var/www/html/src/components/CabinetGallery.tsx": "20", "/var/www/html/src/components/CallToAction.tsx": "21", "/var/www/html/src/components/Footer.tsx": "22", "/var/www/html/src/components/HeroSection.tsx": "23", "/var/www/html/src/components/KitchenGallery.tsx": "24", "/var/www/html/src/components/Navbar.tsx": "25", "/var/www/html/src/components/ProductModal.tsx": "26", "/var/www/html/src/components/Testimonials.tsx": "27", "/var/www/html/src/components/WhyChooseUs.tsx": "28", "/var/www/html/src/components/admin/AdminDashboard.tsx": "29", "/var/www/html/src/components/admin/AdminHeader.tsx": "30", "/var/www/html/src/components/admin/AdminLogin.tsx": "31", "/var/www/html/src/components/admin/AdminSidebar.tsx": "32", "/var/www/html/src/components/admin/sections/CabinetsManagement.tsx": "33", "/var/www/html/src/components/admin/sections/DashboardHome.tsx": "34", "/var/www/html/src/components/admin/sections/FooterManagement.tsx": "35", "/var/www/html/src/components/admin/sections/HeroManagement.tsx": "36", "/var/www/html/src/components/admin/sections/KitchensManagement.tsx": "37", "/var/www/html/src/lib/database/connection.ts": "38", "/var/www/html/src/types/index.ts": "39", "/var/www/html/src/utils/index.ts": "40", "/var/www/html/src/app/privacy/page.tsx": "41", "/var/www/html/src/app/sitemap-page/page.tsx": "42", "/var/www/html/src/app/terms/page.tsx": "43", "/var/www/html/src/app/about/page.tsx": "44", "/var/www/html/src/app/contact/layout.tsx": "45", "/var/www/html/src/app/contact/page.tsx": "46", "/var/www/html/src/app/about/layout.tsx": "47", "/var/www/html/src/app/sitemap-page/layout.tsx": "48", "/var/www/html/src/hooks/useSocialMedia.ts": "49", "/var/www/html/src/components/mobile/MobileAboutPage.tsx": "50", "/var/www/html/src/components/mobile/MobileCabinetsPage.tsx": "51", "/var/www/html/src/components/mobile/MobileContactPage.tsx": "52", "/var/www/html/src/components/mobile/MobileDetector.tsx": "53", "/var/www/html/src/components/mobile/MobileHomePage.tsx": "54", "/var/www/html/src/components/mobile/MobileKitchensPage.tsx": "55", "/var/www/html/src/components/mobile/MobileLayout.tsx": "56", "/var/www/html/src/components/mobile/index.ts": "57"}, {"size": 2083, "mtime": 1752514032717, "results": "58", "hashOfConfig": "59"}, {"size": 437, "mtime": 1752448254202, "results": "60", "hashOfConfig": "59"}, {"size": 2640, "mtime": 1752449425546, "results": "61", "hashOfConfig": "59"}, {"size": 1842, "mtime": 1752443814089, "results": "62", "hashOfConfig": "59"}, {"size": 1940, "mtime": 1752443960773, "results": "63", "hashOfConfig": "59"}, {"size": 3480, "mtime": 1752443880188, "results": "64", "hashOfConfig": "59"}, {"size": 2470, "mtime": 1752443855723, "results": "65", "hashOfConfig": "59"}, {"size": 2633, "mtime": 1752449299658, "results": "66", "hashOfConfig": "59"}, {"size": 1834, "mtime": 1752443775073, "results": "67", "hashOfConfig": "59"}, {"size": 2747, "mtime": 1752448902278, "results": "68", "hashOfConfig": "59"}, {"size": 3199, "mtime": 1752443940998, "results": "69", "hashOfConfig": "59"}, {"size": 9736, "mtime": 1752448213174, "results": "70", "hashOfConfig": "59"}, {"size": 1141, "mtime": 1752448170828, "results": "71", "hashOfConfig": "59"}, {"size": 9683, "mtime": 1752448098050, "results": "72", "hashOfConfig": "59"}, {"size": 1157, "mtime": 1752448056568, "results": "73", "hashOfConfig": "59"}, {"size": 3155, "mtime": 1752456034141, "results": "74", "hashOfConfig": "59"}, {"size": 1357, "mtime": 1752452387912, "results": "75", "hashOfConfig": "59"}, {"size": 2046, "mtime": 1752443688252, "results": "76", "hashOfConfig": "59"}, {"size": 585, "mtime": 1752448821345, "results": "77", "hashOfConfig": "59"}, {"size": 6769, "mtime": 1752447853012, "results": "78", "hashOfConfig": "59"}, {"size": 5785, "mtime": 1752454412690, "results": "79", "hashOfConfig": "59"}, {"size": 7668, "mtime": 1752454884662, "results": "80", "hashOfConfig": "59"}, {"size": 6379, "mtime": 1752444044747, "results": "81", "hashOfConfig": "59"}, {"size": 6743, "mtime": 1752447819582, "results": "82", "hashOfConfig": "59"}, {"size": 4433, "mtime": 1752453526891, "results": "83", "hashOfConfig": "59"}, {"size": 11482, "mtime": 1752454336173, "results": "84", "hashOfConfig": "59"}, {"size": 6122, "mtime": 1752452052382, "results": "85", "hashOfConfig": "59"}, {"size": 6906, "mtime": 1752452145067, "results": "86", "hashOfConfig": "59"}, {"size": 2049, "mtime": 1752511864692, "results": "87", "hashOfConfig": "59"}, {"size": 4706, "mtime": 1752448393256, "results": "88", "hashOfConfig": "59"}, {"size": 4778, "mtime": 1752511807013, "results": "89", "hashOfConfig": "59"}, {"size": 3669, "mtime": 1752448364330, "results": "90", "hashOfConfig": "59"}, {"size": 7141, "mtime": 1752448617590, "results": "91", "hashOfConfig": "59"}, {"size": 7289, "mtime": 1752448446801, "results": "92", "hashOfConfig": "59"}, {"size": 10529, "mtime": 1752448664217, "results": "93", "hashOfConfig": "59"}, {"size": 10749, "mtime": 1752448499122, "results": "94", "hashOfConfig": "59"}, {"size": 7094, "mtime": 1752448579810, "results": "95", "hashOfConfig": "59"}, {"size": 2645, "mtime": 1752443743647, "results": "96", "hashOfConfig": "59"}, {"size": 2504, "mtime": 1752448704193, "results": "97", "hashOfConfig": "59"}, {"size": 5343, "mtime": 1752448737884, "results": "98", "hashOfConfig": "59"}, {"size": 7932, "mtime": 1752453251594, "results": "99", "hashOfConfig": "59"}, {"size": 9688, "mtime": 1752454798908, "results": "100", "hashOfConfig": "59"}, {"size": 10659, "mtime": 1752453273138, "results": "101", "hashOfConfig": "59"}, {"size": 12781, "mtime": 1752454556939, "results": "102", "hashOfConfig": "59"}, {"size": 991, "mtime": 1752453479793, "results": "103", "hashOfConfig": "59"}, {"size": 15820, "mtime": 1752454723572, "results": "104", "hashOfConfig": "59"}, {"size": 1041, "mtime": 1752454600651, "results": "105", "hashOfConfig": "59"}, {"size": 417, "mtime": 1752454821787, "results": "106", "hashOfConfig": "59"}, {"size": 5319, "mtime": 1752454917856, "results": "107", "hashOfConfig": "59"}, {"size": 9649, "mtime": 1752458667553, "results": "108", "hashOfConfig": "59"}, {"size": 13304, "mtime": 1752459720154, "results": "109", "hashOfConfig": "59"}, {"size": 11611, "mtime": 1752459606495, "results": "110", "hashOfConfig": "59"}, {"size": 2925, "mtime": 1752511030176, "results": "111", "hashOfConfig": "59"}, {"size": 7999, "mtime": 1752514769724, "results": "112", "hashOfConfig": "59"}, {"size": 13310, "mtime": 1752459696652, "results": "113", "hashOfConfig": "59"}, {"size": 2894, "mtime": 1752459742279, "results": "114", "hashOfConfig": "59"}, {"size": 475, "mtime": 1752456115579, "results": "115", "hashOfConfig": "59"}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "nkml1y", {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/src/app/admin/AdminPageContent.tsx", [], [], "/var/www/html/src/app/admin/page.tsx", [], [], "/var/www/html/src/app/api/cabinets/[id]/route.ts", [], [], "/var/www/html/src/app/api/cabinets/route.ts", [], [], "/var/www/html/src/app/api/categories/route.ts", [], [], "/var/www/html/src/app/api/footer/route.ts", [], [], "/var/www/html/src/app/api/hero/route.ts", [], [], "/var/www/html/src/app/api/kitchens/[id]/route.ts", [], [], "/var/www/html/src/app/api/kitchens/route.ts", [], [], "/var/www/html/src/app/api/uploads/route.ts", [], [], "/var/www/html/src/app/api/why-choose-us/route.ts", [], [], "/var/www/html/src/app/cabinets/CabinetsPageContent.tsx", [], [], "/var/www/html/src/app/cabinets/page.tsx", [], [], "/var/www/html/src/app/kitchens/KitchensPageContent.tsx", [], [], "/var/www/html/src/app/kitchens/page.tsx", [], [], "/var/www/html/src/app/layout.tsx", [], [], "/var/www/html/src/app/manifest.ts", [], [], "/var/www/html/src/app/page.tsx", [], [], "/var/www/html/src/app/sitemap.ts", [], [], "/var/www/html/src/components/CabinetGallery.tsx", [], [], "/var/www/html/src/components/CallToAction.tsx", [], [], "/var/www/html/src/components/Footer.tsx", [], [], "/var/www/html/src/components/HeroSection.tsx", [], [], "/var/www/html/src/components/KitchenGallery.tsx", [], [], "/var/www/html/src/components/Navbar.tsx", [], [], "/var/www/html/src/components/ProductModal.tsx", [], [], "/var/www/html/src/components/Testimonials.tsx", [], [], "/var/www/html/src/components/WhyChooseUs.tsx", [], [], "/var/www/html/src/components/admin/AdminDashboard.tsx", [], [], "/var/www/html/src/components/admin/AdminHeader.tsx", [], [], "/var/www/html/src/components/admin/AdminLogin.tsx", [], [], "/var/www/html/src/components/admin/AdminSidebar.tsx", [], [], "/var/www/html/src/components/admin/sections/CabinetsManagement.tsx", [], [], "/var/www/html/src/components/admin/sections/DashboardHome.tsx", [], [], "/var/www/html/src/components/admin/sections/FooterManagement.tsx", [], [], "/var/www/html/src/components/admin/sections/HeroManagement.tsx", [], [], "/var/www/html/src/components/admin/sections/KitchensManagement.tsx", [], [], "/var/www/html/src/lib/database/connection.ts", [], [], "/var/www/html/src/types/index.ts", [], [], "/var/www/html/src/utils/index.ts", [], [], "/var/www/html/src/app/privacy/page.tsx", [], [], "/var/www/html/src/app/sitemap-page/page.tsx", [], [], "/var/www/html/src/app/terms/page.tsx", [], [], "/var/www/html/src/app/about/page.tsx", [], [], "/var/www/html/src/app/contact/layout.tsx", [], [], "/var/www/html/src/app/contact/page.tsx", [], [], "/var/www/html/src/app/about/layout.tsx", [], [], "/var/www/html/src/app/sitemap-page/layout.tsx", [], [], "/var/www/html/src/hooks/useSocialMedia.ts", [], [], "/var/www/html/src/components/mobile/MobileAboutPage.tsx", [], [], "/var/www/html/src/components/mobile/MobileCabinetsPage.tsx", [], [], "/var/www/html/src/components/mobile/MobileContactPage.tsx", [], [], "/var/www/html/src/components/mobile/MobileDetector.tsx", [], [], "/var/www/html/src/components/mobile/MobileHomePage.tsx", [], [], "/var/www/html/src/components/mobile/MobileKitchensPage.tsx", [], [], "/var/www/html/src/components/mobile/MobileLayout.tsx", [], [], "/var/www/html/src/components/mobile/index.ts", [], []]