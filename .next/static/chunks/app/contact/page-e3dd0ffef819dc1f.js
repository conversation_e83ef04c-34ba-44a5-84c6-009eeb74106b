(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{6967:(e,r,t)=>{Promise.resolve().then(t.bind(t,8983))},7012:(e,r,t)=>{"use strict";t.d(r,{X:()=>l});var s=t(2115);let l=()=>{let[e,r]=(0,s.useState)([]),[t,l]=(0,s.useState)([]),[a,i]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/footer");if(e.ok){let t=await e.json();r(t.socialMedia||[]),l(t.contactInfo||[])}}catch(e){console.error("Error fetching social media data:",e),r([{id:1,platform:"واتساب",url:"https://wa.me/966557611105",icon:"ri-whatsapp-line"},{id:2,platform:"تويتر",url:"https://twitter.com",icon:"ri-twitter-x-line"},{id:3,platform:"إنستغرام",url:"https://instagram.com",icon:"ri-instagram-line"},{id:4,platform:"فيسبوك",url:"https://facebook.com",icon:"ri-facebook-line"},{id:5,platform:"سناب شات",url:"https://snapchat.com",icon:"ri-snapchat-line"},{id:6,platform:"تيك توك",url:"https://tiktok.com",icon:"ri-tiktok-line"}]),l([{id:1,icon:"ri-phone-line",text:"+966 55 761 1105",type:"phone"}])}finally{i(!1)}})()},[]),{socialMedia:e,contactInfo:t,loading:a,getWhatsAppLink:r=>{let t=e.find(e=>e.platform.toLowerCase().includes("واتساب")||e.platform.toLowerCase().includes("whatsapp"));if(t){let e=t.url.includes("wa.me")?t.url:"https://wa.me/966557611105";return r?"".concat(e,"?text=").concat(encodeURIComponent(r)):e}return"https://wa.me/966557611105".concat(r?"?text=".concat(encodeURIComponent(r)):"")},getPhoneNumber:()=>{let e=t.find(e=>"phone"===e.type);return e?e.text.replace(/\s/g,""):"+966557611105"},getSocialMediaLink:r=>{let t=r.toLowerCase(),s=e.find(e=>{let r=e.platform.toLowerCase();return r.includes(t)||t.includes(r)||t.includes("تويتر")&&r.includes("twitter")||t.includes("twitter")&&r.includes("تويتر")||t.includes("إنستغرام")&&r.includes("instagram")||t.includes("instagram")&&r.includes("إنستغرام")||t.includes("فيسبوك")&&r.includes("facebook")||t.includes("facebook")&&r.includes("فيسبوك")||t.includes("سناب")&&r.includes("snap")||t.includes("snap")&&r.includes("سناب")||t.includes("تيك")&&r.includes("tik")||t.includes("tik")&&r.includes("تيك")});return(null==s?void 0:s.url)||"#"},getSocialMediaIcon:r=>{let t=r.toLowerCase(),s=e.find(e=>{let r=e.platform.toLowerCase();return r.includes(t)||t.includes(r)||t.includes("تويتر")&&r.includes("twitter")||t.includes("twitter")&&r.includes("تويتر")||t.includes("إنستغرام")&&r.includes("instagram")||t.includes("instagram")&&r.includes("إنستغرام")||t.includes("فيسبوك")&&r.includes("facebook")||t.includes("facebook")&&r.includes("فيسبوك")||t.includes("سناب")&&r.includes("snap")||t.includes("snap")&&r.includes("سناب")||t.includes("تيك")&&r.includes("tik")||t.includes("tik")&&r.includes("تيك")});return(null==s?void 0:s.icon)||"ri-link"}}}},8983:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(5155),l=t(2115),a=t(5494),i=t(6821),n=t(6874),o=t.n(n),c=t(7012);function d(){let{getWhatsAppLink:e,getPhoneNumber:r}=(0,c.X)(),[t,n]=(0,l.useState)({name:"",phone:"",email:"",subject:"",message:"",projectType:"kitchen"}),[d,m]=(0,l.useState)(!1),[x,p]=(0,l.useState)("idle"),u=e=>{let{name:r,value:t}=e.target;n(e=>({...e,[r]:t}))},h=async e=>{e.preventDefault(),m(!0);try{await new Promise(e=>setTimeout(e,1e3)),p("success"),n({name:"",phone:"",email:"",subject:"",message:"",projectType:"kitchen"})}catch(e){p("error")}finally{m(!1)}};return(0,s.jsxs)("div",{className:"bg-gray-50",dir:"rtl",children:[(0,s.jsx)(a.default,{}),(0,s.jsxs)("main",{className:"pt-20",children:[(0,s.jsx)("section",{className:"bg-gradient-to-br from-primary-600 to-primary-800 py-20",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"تواصل معنا"}),(0,s.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto leading-relaxed",children:"نحن هنا لمساعدتك في تحويل أحلامك إلى واقع. تواصل معنا للحصول على استشارة مجانية"})]})}),(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:"ri-phone-line",title:"اتصل بنا",info:r(),action:"tel:".concat(r()),color:"from-blue-500 to-blue-600"},{icon:"ri-whatsapp-line",title:"واتساب",info:r(),action:e(),color:"from-green-500 to-green-600"},{icon:"ri-mail-line",title:"البريد الإلكتروني",info:"<EMAIL>",action:"mailto:<EMAIL>",color:"from-purple-500 to-purple-600"},{icon:"ri-map-pin-line",title:"العنوان",info:"الرياض، المملكة العربية السعودية",action:"#map",color:"from-orange-500 to-orange-600"}].map((e,r)=>(0,s.jsxs)(o(),{href:e.action,target:e.action.startsWith("http")?"_blank":void 0,rel:e.action.startsWith("http")?"noopener noreferrer":void 0,className:"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-center group",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br ".concat(e.color," rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"),children:(0,s.jsx)("i",{className:"".concat(e.icon," text-white text-2xl")})}),(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.info})]},r))})})}),(0,s.jsx)("section",{className:"py-16 bg-gray-50",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"أرسل لنا رسالة"}),"success"===x&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-check-circle-line text-green-500 text-xl mr-3"}),(0,s.jsx)("p",{className:"text-green-700",children:"تم إرسال رسالتك بنجاح! سنتواصل معك قريباً."})]})}),"error"===x&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-500 text-xl mr-3"}),(0,s.jsx)("p",{className:"text-red-700",children:"حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى."})]})}),(0,s.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:t.name,onChange:u,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"أدخل اسمك الكامل"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"رقم الهاتف *"}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:t.phone,onChange:u,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"05xxxxxxxx"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:u,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"projectType",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع المشروع *"}),(0,s.jsxs)("select",{id:"projectType",name:"projectType",value:t.projectType,onChange:u,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",children:[(0,s.jsx)("option",{value:"kitchen",children:"مطبخ"}),(0,s.jsx)("option",{value:"cabinet",children:"خزانة"}),(0,s.jsx)("option",{value:"both",children:"مطبخ وخزانة"}),(0,s.jsx)("option",{value:"consultation",children:"استشارة"}),(0,s.jsx)("option",{value:"other",children:"أخرى"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"الموضوع"}),(0,s.jsx)("input",{type:"text",id:"subject",name:"subject",value:t.subject,onChange:u,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"موضوع الرسالة"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"الرسالة *"}),(0,s.jsx)("textarea",{id:"message",name:"message",value:t.message,onChange:u,required:!0,rows:5,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none",placeholder:"اكتب رسالتك هنا..."})]}),(0,s.jsx)("button",{type:"submit",disabled:d,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-4 px-6 rounded-lg font-bold text-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-3",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"جاري الإرسال..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-send-plane-line text-xl"}),"إرسال الرسالة"]})})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"موقعنا على الخريطة"}),(0,s.jsx)("div",{id:"map",className:"w-full h-96 bg-gray-200 rounded-lg overflow-hidden",children:(0,s.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.2!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f03890d489399%3A0xba974d1c98e79fd5!2sRiyadh%20Saudi%20Arabia!5e0!3m2!1sen!2s!4v1640000000000!5m2!1sen!2s",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"موقع عجائب الخبراء"})}),(0,s.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,s.jsx)("i",{className:"ri-map-pin-line text-primary-500 text-xl mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:"العنوان"}),(0,s.jsx)("p",{className:"text-gray-600",children:"الرياض، المملكة العربية السعودية"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,s.jsx)("i",{className:"ri-time-line text-primary-500 text-xl mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:"ساعات العمل"}),(0,s.jsx)("p",{className:"text-gray-600",children:"السبت - الخميس: 8:00 ص - 6:00 م"}),(0,s.jsx)("p",{className:"text-gray-600",children:"الجمعة: مغلق"})]})]})]})]})]})})}),(0,s.jsx)("section",{className:"py-16 bg-gradient-to-r from-primary-600 to-primary-800",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"تحتاج مساعدة فورية؟"}),(0,s.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"تواصل معنا مباشرة للحصول على رد سريع واستشارة فورية"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,s.jsxs)(o(),{href:e(),target:"_blank",rel:"noopener noreferrer",className:"bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center",children:[(0,s.jsx)("i",{className:"ri-whatsapp-line text-xl"}),"واتساب فوري"]}),(0,s.jsxs)(o(),{href:"tel:".concat(r()),className:"bg-white text-primary-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center",children:[(0,s.jsx)("i",{className:"ri-phone-line text-xl"}),"اتصال مباشر"]})]})]})})]}),(0,s.jsx)(i.default,{})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,299,202,441,684,358],()=>r(6967)),_N_E=e.O()}]);