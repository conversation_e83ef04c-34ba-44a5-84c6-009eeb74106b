self.__BUILD_MANIFEST=function(e,r,s,t){return{__rewrites:{afterFiles:[{has:void 0,source:"/uploads/:path*",destination:"/api/uploads/:path*"}],beforeFiles:[],fallback:[]},__routerFilterStatic:{numItems:19,errorRate:1e-4,numBits:365,numHashes:14,bitArray:[1,0,0,1,r,e,e,e,r,e,e,r,e,e,r,r,r,r,r,r,e,e,e,r,r,e,e,e,r,e,r,r,e,r,r,r,e,r,r,r,r,r,r,r,e,r,e,e,e,r,e,r,r,r,e,r,e,r,r,e,r,e,r,e,e,r,e,r,e,r,e,e,e,r,e,r,r,r,e,e,r,r,r,r,e,r,r,r,e,r,e,r,e,e,e,r,e,r,e,e,r,r,r,r,r,e,e,r,r,r,e,e,r,r,e,e,e,e,r,r,e,e,e,e,e,e,r,e,e,r,e,r,e,r,r,e,e,e,e,e,e,r,r,r,e,r,e,r,r,r,r,e,r,r,r,e,r,r,e,e,r,e,e,r,e,e,r,r,r,r,r,r,r,r,e,e,r,e,r,r,r,r,r,e,e,e,r,r,r,e,e,e,e,e,r,r,r,r,r,r,r,e,r,e,e,e,e,r,e,e,r,r,e,e,r,r,e,r,e,e,e,r,r,r,e,e,r,e,e,e,e,r,r,e,e,e,e,r,e,e,r,r,e,e,r,r,e,r,e,e,e,r,r,r,e,e,e,r,r,e,e,e,e,r,e,e,e,r,r,e,r,r,r,e,r,r,r,r,e,e,r,e,e,r,r,e,e,e,r,r,r,r,e,e,e,e,r,r,r,r,e,r,e,e,e,e,e,r,r,r,r,e,r,e,e,r,r,e,r,e,r,r,e,r,e,e,e,e,e,e,e,e,r,r,r,e,e,e,e,r,e,r,e,e,e,r,r,r,e,e,r,e,e,e,e,e,r,e,e,e,r,r,r,r,e]},__routerFilterDynamic:{numItems:2,errorRate:1e-4,numBits:39,numHashes:14,bitArray:[e,r,e,r,r,e,e,r,r,r,e,e,e,e,r,r,e,r,e,r,e,e,e,e,r,e,e,r,e,e,r,r,r,e,e,r,r,r,e]},"/_error":["static/chunks/pages/_error-1108a40f9b38b328.js"],sortedPages:["/_app","/_error"]}}(1,0,1e-4,14),self.__BUILD_MANIFEST_CB&&self.__BUILD_MANIFEST_CB();