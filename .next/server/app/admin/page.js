(()=>{var e={};e.id=698,e.ids=[698],e.modules={93:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var a=t(687),s=t(3210);let n=({onLogin:e})=>{let[r,t]=(0,s.useState)({username:"",password:""}),[n,i]=(0,s.useState)(!1),[o,d]=(0,s.useState)(""),l=async t=>{t.preventDefault(),i(!0),d(""),"admin"===r.username&&"admin123"===r.password?e(!0):(d("اسم المستخدم أو كلمة المرور غير صحيحة"),e(!1)),i(!1)};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full animate-fade-in",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:"ع"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"لوحة الإدارة"}),(0,a.jsx)("p",{className:"text-gray-600",children:"عجائب الخبراء - نظام إدارة المحتوى"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("form",{onSubmit:l,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المستخدم"}),(0,a.jsx)("input",{type:"text",id:"username",value:r.username,onChange:e=>t({...r,username:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"أدخل اسم المستخدم",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,a.jsx)("input",{type:"password",id:"password",value:r.password,onChange:e=>t({...r,password:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"أدخل كلمة المرور",required:!0})]}),o&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in",children:o}),(0,a.jsx)("button",{type:"submit",disabled:n,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:n?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"بيانات تجريبية:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,a.jsx)("strong",{children:"المستخدم:"})," admin",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"كلمة المرور:"})," admin123"]})]})]}),(0,a.jsx)("div",{className:"text-center mt-8 text-sm text-gray-500",children:"\xa9 2024 عجائب الخبراء. جميع الحقوق محفوظة."})]})})},i=()=>{let[e,r]=(0,s.useState)(!1),[t,i]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{(()=>{let e=localStorage.getItem("admin_token"),t=localStorage.getItem("admin_token_expiry");e&&t&&(new Date().getTime()<parseInt(t)?r(!0):(localStorage.removeItem("admin_token"),localStorage.removeItem("admin_token_expiry"))),i(!1)})()},[]),t)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 flex items-center justify-center",dir:"rtl",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30",dir:"rtl",children:e?(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"لوحة الإدارة"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"مرحباً بك في لوحة إدارة عجائب الخبراء"}),(0,a.jsx)("button",{onClick:()=>{r(!1),localStorage.removeItem("admin_token"),localStorage.removeItem("admin_token_expiry")},className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600",children:"تسجيل الخروج"})]}):(0,a.jsx)(n,{onLogin:e=>{if(e){r(!0);let e=new Date().getTime()+864e5;localStorage.setItem("admin_token","authenticated"),localStorage.setItem("admin_token_expiry",e.toString())}}})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1046:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/var/www/html/src/app/admin/AdminPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/var/www/html/src/app/admin/AdminPageContent.tsx","default")},1132:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>n});var a=t(7413),s=t(1046);let n={title:"لوحة الإدارة - عجائب الخبراء",description:"لوحة إدارة موقع عجائب الخبراء - منطقة محظورة للمديرين فقط",robots:"noindex, nofollow, noarchive, nosnippet"};function i(){return(0,a.jsx)(s.default,{})}},2161:(e,r,t)=>{Promise.resolve().then(t.bind(t,93))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5172:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>l});var a=t(5239),s=t(8088),n=t(8170),i=t.n(n),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"/var/www/html/src/app/admin/page.tsx"]}]},{metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/var/www/html/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,m=["/var/www/html/src/app/admin/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5713:(e,r,t)=>{Promise.resolve().then(t.bind(t,1046))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,425,429],()=>t(5172));module.exports=a})();