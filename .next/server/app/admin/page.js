(()=>{var e={};e.id=698,e.ids=[698],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1046:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/var/www/html/src/app/admin/AdminPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/var/www/html/src/app/admin/AdminPageContent.tsx","default")},1132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>i});var s=r(7413),a=r(1046);let i={title:"لوحة الإدارة - عجائب الخبراء",description:"لوحة إدارة موقع عجائب الخبراء - منطقة محظورة للمديرين فقط",robots:"noindex, nofollow, noarchive, nosnippet"};function l(){return(0,s.jsx)(a.default,{})}},2161:(e,t,r)=>{Promise.resolve().then(r.bind(r,2287))},2287:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(687),a=r(3210);let i=({onLogin:e})=>{let[t,r]=(0,a.useState)({username:"",password:""}),[i,l]=(0,a.useState)(!1),[n,o]=(0,a.useState)(""),d=async r=>{r.preventDefault(),l(!0),o(""),"admin"===t.username&&"admin123"===t.password?e(!0):(o("اسم المستخدم أو كلمة المرور غير صحيحة"),e(!1)),l(!1)};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full animate-fade-in",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("span",{className:"text-white font-bold text-2xl",children:"ع"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"لوحة الإدارة"}),(0,s.jsx)("p",{className:"text-gray-600",children:"عجائب الخبراء - نظام إدارة المحتوى"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("form",{onSubmit:d,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم المستخدم"}),(0,s.jsx)("input",{type:"text",id:"username",value:t.username,onChange:e=>r({...t,username:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"أدخل اسم المستخدم",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,s.jsx)("input",{type:"password",id:"password",value:t.password,onChange:e=>r({...t,password:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors",placeholder:"أدخل كلمة المرور",required:!0})]}),n&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in",children:n}),(0,s.jsx)("button",{type:"submit",disabled:i,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"بيانات تجريبية:"}),(0,s.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,s.jsx)("strong",{children:"المستخدم:"})," admin",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"كلمة المرور:"})," admin123"]})]})]}),(0,s.jsx)("div",{className:"text-center mt-8 text-sm text-gray-500",children:"\xa9 2024 عجائب الخبراء. جميع الحقوق محفوظة."})]})})},l=({activeSection:e,onSectionChange:t,isOpen:r,onToggle:a})=>(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:`fixed top-0 right-0 h-full w-64 bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${r?"translate-x-0":"translate-x-full"} lg:translate-x-0`,children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:"ع"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-bold text-gray-900",children:"لوحة الإدارة"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"عجائب الخبراء"})]})]})}),(0,s.jsx)("nav",{className:"p-4",children:(0,s.jsx)("ul",{className:"space-y-2",children:[{id:"dashboard",name:"الرئيسية",icon:"\uD83C\uDFE0",description:"نظرة عامة على النظام"},{id:"hero",name:"البانر الرئيسي",icon:"\uD83C\uDFAF",description:"إدارة القسم الرئيسي"},{id:"kitchens",name:"المطابخ",icon:"\uD83C\uDFE0",description:"إدارة المطابخ والصور"},{id:"cabinets",name:"الخزانات",icon:"\uD83D\uDDC4️",description:"إدارة الخزانات والصور"},{id:"footer",name:"التذييل",icon:"\uD83D\uDCC4",description:"إدارة معلومات التواصل"}].map(r=>(0,s.jsx)("li",{children:(0,s.jsx)("button",{onClick:()=>{t(r.id),window.innerWidth<1024&&a()},className:`w-full text-right p-4 rounded-xl transition-all duration-200 group ${e===r.id?"bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg":"text-gray-700 hover:bg-gray-100"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)("span",{className:"text-2xl",children:r.icon}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"font-medium",children:r.name}),(0,s.jsx)("div",{className:`text-sm ${e===r.id?"text-white/80":"text-gray-500"}`,children:r.description})]})]})})},r.id))})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"text-center text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"\xa9 2024 عجائب الخبراء"}),(0,s.jsx)("p",{children:"الإصدار 2.0"})]})})]})});var n=r(8920),o=r(6001);let d=({onLogout:e,onMenuToggle:t})=>{let[r,i]=(0,a.useState)(!1);return(0,s.jsxs)("header",{className:"bg-white shadow-sm border-b border-gray-200 px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("button",{onClick:t,className:"lg:hidden p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,s.jsxs)("div",{className:"flex-1 lg:flex-none",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"لوحة الإدارة"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"إدارة محتوى موقع عجائب الخبراء"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>i(!r),className:"flex items-center space-x-3 space-x-reverse p-2 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-sm",children:"م"})}),(0,s.jsxs)("div",{className:"hidden sm:block text-right",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"المدير"}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"<EMAIL>"})]}),(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),(0,s.jsx)(n.N,{children:r&&(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,s.jsxs)("div",{className:"px-4 py-2 border-b border-gray-100",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"المدير"}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"<EMAIL>"})]}),(0,s.jsxs)("button",{onClick:()=>{i(!1)},className:"w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{children:"\uD83D\uDC64"}),(0,s.jsx)("span",{children:"الملف الشخصي"})]}),(0,s.jsxs)("button",{onClick:()=>{i(!1)},className:"w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{children:"⚙️"}),(0,s.jsx)("span",{children:"الإعدادات"})]}),(0,s.jsx)("div",{className:"border-t border-gray-100 mt-2 pt-2",children:(0,s.jsxs)("button",{onClick:()=>{i(!1),e()},className:"w-full text-right px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{children:"\uD83D\uDEAA"}),(0,s.jsx)("span",{children:"تسجيل الخروج"})]})})]})})]})]}),r&&(0,s.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>i(!1)})]})},c=()=>{let[e,t]=(0,a.useState)({kitchens:0,cabinets:0,totalImages:0,categories:0}),[r,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{try{let[e,r,s]=await Promise.all([fetch("/api/kitchens"),fetch("/api/cabinets"),fetch("/api/categories")]),[a,i,l]=await Promise.all([e.json(),r.json(),s.json()]),n=a.reduce((e,t)=>e+(t.images?.length||0),0)+i.reduce((e,t)=>e+(t.images?.length||0),0);t({kitchens:a.length,cabinets:i.length,totalImages:n,categories:l.length})}catch(e){console.error("Error fetching stats:",e)}finally{i(!1)}})()},[]);let l=[{title:"المطابخ",value:e.kitchens,icon:"\uD83C\uDFE0",color:"from-blue-500 to-blue-600",bgColor:"from-blue-50 to-blue-100"},{title:"الخزانات",value:e.cabinets,icon:"\uD83D\uDDC4️",color:"from-purple-500 to-purple-600",bgColor:"from-purple-50 to-purple-100"},{title:"إجمالي الصور",value:e.totalImages,icon:"\uD83D\uDCF7",color:"from-green-500 to-green-600",bgColor:"from-green-50 to-green-100"},{title:"الفئات",value:e.categories,icon:"\uD83D\uDCC2",color:"from-orange-500 to-orange-600",bgColor:"from-orange-50 to-orange-100"}];return r?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl p-8 text-white",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"مرحباً بك في لوحة الإدارة"}),(0,s.jsx)("p",{className:"text-lg opacity-90",children:"إدارة محتوى موقع عجائب الخبراء بسهولة وفعالية"}),(0,s.jsxs)("div",{className:"mt-6 text-sm opacity-80",children:["آخر تحديث: ",new Date().toLocaleDateString("ar-SA")]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:l.map((e,t)=>(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:`bg-gradient-to-br ${e.bgColor} rounded-2xl p-6 border border-white/50`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:`w-12 h-12 bg-gradient-to-br ${e.color} rounded-xl flex items-center justify-center text-white text-2xl`,children:e.icon}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:e.value}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:e.title})]})]})},e.title))}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"إجراءات سريعة"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{title:"إضافة مطبخ جديد",description:"أضف مطبخ جديد إلى المعرض",icon:"➕",action:"kitchens"},{title:"إضافة خزانة جديدة",description:"أضف خزانة جديدة إلى المعرض",icon:"➕",action:"cabinets"},{title:"تحديث البانر الرئيسي",description:"عدل محتوى الصفحة الرئيسية",icon:"✏️",action:"hero"},{title:"إدارة معلومات التواصل",description:"حدث معلومات التواصل في التذييل",icon:"\uD83D\uDCDE",action:"footer"}].map((e,t)=>(0,s.jsx)(o.P.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-right group border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center text-2xl group-hover:from-primary-100 group-hover:to-primary-200 transition-all duration-300",children:e.icon}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]})},e.title))})]}),(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"معلومات النظام"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"الإصدار:"}),(0,s.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:"2.0.0"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"آخر نسخة احتياطية:"}),(0,s.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:"اليوم"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"حالة النظام:"}),(0,s.jsx)("span",{className:"font-medium text-green-600 mr-2",children:"يعمل بشكل طبيعي"})]})]})]})]})},x=()=>{let[e,t]=(0,a.useState)({id:null,title:"",subtitle:"",background_image:null,primary_button_text:"",secondary_button_text:"",is_active:1}),[r,i]=(0,a.useState)(!0),[l,n]=(0,a.useState)(!1),[d,c]=(0,a.useState)("");(0,a.useEffect)(()=>{x()},[]);let x=async()=>{try{let e=await fetch("/api/hero");if(e.ok){let r=await e.json();t(r)}}catch(e){console.error("Error fetching hero data:",e),c("خطأ في تحميل البيانات")}finally{i(!1)}},m=async()=>{n(!0),c("");try{(await fetch("/api/hero",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?(c("تم حفظ البيانات بنجاح"),setTimeout(()=>c(""),3e3)):c("خطأ في حفظ البيانات")}catch(e){console.error("Error saving hero data:",e),c("خطأ في حفظ البيانات")}finally{n(!1)}},h=async r=>{let s=r.target.files?.[0];if(!s)return;let a=new FormData;a.append("file",s),a.append("type","hero");try{let r=await fetch("/api/uploads",{method:"POST",body:a});if(r.ok){let s=await r.json();t({...e,background_image:s.url}),c("تم رفع الصورة بنجاح"),setTimeout(()=>c(""),3e3)}else c("خطأ في رفع الصورة")}catch(e){console.error("Error uploading image:",e),c("خطأ في رفع الصورة")}};return r?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة البانر الرئيسي"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"تحكم في محتوى القسم الرئيسي للموقع"})]}),(0,s.jsx)("button",{onClick:m,disabled:l,className:"bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-300 disabled:opacity-50",children:l?"جاري الحفظ...":"حفظ التغييرات"})]}),d&&(0,s.jsx)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`p-4 rounded-lg ${d.includes("نجاح")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:d}),(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان الرئيسي"}),(0,s.jsx)("input",{type:"text",value:e.title,onChange:r=>t({...e,title:r.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"أدخل العنوان الرئيسي"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"العنوان الفرعي"}),(0,s.jsx)("textarea",{value:e.subtitle,onChange:r=>t({...e,subtitle:r.target.value}),rows:3,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"أدخل العنوان الفرعي"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"نص الزر الأول"}),(0,s.jsx)("input",{type:"text",value:e.primary_button_text,onChange:r=>t({...e,primary_button_text:r.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"نص الزر الأول"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"نص الزر الثاني"}),(0,s.jsx)("input",{type:"text",value:e.secondary_button_text,onChange:r=>t({...e,secondary_button_text:r.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"نص الزر الثاني"})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"صورة الخلفية"}),e.background_image&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("img",{src:e.background_image,alt:"صورة الخلفية الحالية",className:"w-full h-48 object-cover rounded-lg border border-gray-200"}),(0,s.jsx)("button",{onClick:()=>t({...e,background_image:null}),className:"mt-2 text-red-600 hover:text-red-700 text-sm",children:"حذف الصورة"})]}),(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors",children:[(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:h,className:"hidden",id:"hero-image-upload"}),(0,s.jsxs)("label",{htmlFor:"hero-image-upload",className:"cursor-pointer",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCF7"}),(0,s.jsx)("div",{className:"text-lg font-medium text-gray-900 mb-2",children:"رفع صورة جديدة"}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"اختر صورة بصيغة JPG أو PNG (الحد الأقصى 5MB)"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"أو أدخل رابط الصورة"}),(0,s.jsx)("input",{type:"url",value:e.background_image||"",onChange:r=>t({...e,background_image:r.target.value}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"https://example.com/image.jpg"})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-6",children:"معاينة"}),(0,s.jsx)("div",{className:"relative h-64 rounded-lg overflow-hidden",style:{backgroundImage:e.background_image?`linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(${e.background_image})`:"linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%)",backgroundSize:"cover",backgroundPosition:"center"},children:(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center text-center text-white p-8",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-4",children:e.title||"العنوان الرئيسي"}),(0,s.jsx)("p",{className:"text-lg mb-6",children:e.subtitle||"العنوان الفرعي"}),(0,s.jsxs)("div",{className:"space-x-4 space-x-reverse",children:[(0,s.jsx)("button",{className:"bg-white text-primary-600 px-6 py-2 rounded-full font-medium",children:e.primary_button_text||"الزر الأول"}),(0,s.jsx)("button",{className:"border-2 border-white text-white px-6 py-2 rounded-full font-medium",children:e.secondary_button_text||"الزر الثاني"})]})]})})})]})]})},m=()=>{let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[l,n]=(0,a.useState)(!0),[d,c]=(0,a.useState)("");(0,a.useEffect)(()=>{x()},[]);let x=async()=>{try{let[e,r]=await Promise.all([fetch("/api/kitchens"),fetch("/api/categories?type=kitchen")]);if(e.ok){let r=await e.json();t(r)}if(r.ok){let e=await r.json();i(e)}}catch(e){console.error("Error fetching data:",e),c("خطأ في تحميل البيانات")}finally{n(!1)}},m=async r=>{if(confirm("هل أنت متأكد من حذف هذا المطبخ؟"))try{(await fetch(`/api/kitchens/${r}`,{method:"DELETE"})).ok?(t(e.filter(e=>e.id!==r)),c("تم حذف المطبخ بنجاح"),setTimeout(()=>c(""),3e3)):c("خطأ في حذف المطبخ")}catch(e){console.error("Error deleting kitchen:",e),c("خطأ في حذف المطبخ")}};return l?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة المطابخ"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"إضافة وتعديل وحذف المطابخ"})]}),(0,s.jsxs)("button",{className:"bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-300 flex items-center gap-2",children:[(0,s.jsx)("span",{children:"➕"}),"إضافة مطبخ جديد"]})]}),d&&(0,s.jsx)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`p-4 rounded-lg ${d.includes("نجاح")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:d}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:e.length}),(0,s.jsx)("div",{className:"text-gray-600",children:"إجمالي المطابخ"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.reduce((e,t)=>e+(t.images?.length||0),0)}),(0,s.jsx)("div",{className:"text-gray-600",children:"إجمالي الصور"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:r.length}),(0,s.jsx)("div",{className:"text-gray-600",children:"الفئات المتاحة"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,s.jsxs)("div",{className:"h-48 bg-gray-200 relative",children:[e.images&&e.images.length>0?(0,s.jsx)("img",{src:e.images[0].image_url,alt:e.title,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-500",children:"لا توجد صور"}),e.category_name&&(0,s.jsx)("div",{className:"absolute top-2 right-2 bg-primary-500 text-white px-2 py-1 rounded text-sm",children:e.category_name})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,s.jsxs)("span",{children:[e.images?.length||0," صورة"]}),(0,s.jsxs)("span",{children:["ID: ",e.id]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{className:"flex-1 bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 transition-colors text-sm",children:"تعديل"}),(0,s.jsx)("button",{onClick:()=>m(e.id),className:"flex-1 bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 transition-colors text-sm",children:"حذف"})]})]})]},e.id))}),0===e.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFE0"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"لا توجد مطابخ"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"ابدأ بإضافة أول مطبخ إلى المعرض"}),(0,s.jsx)("button",{className:"bg-primary-500 text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors",children:"إضافة مطبخ جديد"})]})]})},h=()=>{let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[l,n]=(0,a.useState)(!0),[d,c]=(0,a.useState)("");(0,a.useEffect)(()=>{x()},[]);let x=async()=>{try{let[e,r]=await Promise.all([fetch("/api/cabinets"),fetch("/api/categories?type=cabinet")]);if(e.ok){let r=await e.json();t(r)}if(r.ok){let e=await r.json();i(e)}}catch(e){console.error("Error fetching data:",e),c("خطأ في تحميل البيانات")}finally{n(!1)}},m=async r=>{if(confirm("هل أنت متأكد من حذف هذه الخزانة؟"))try{(await fetch(`/api/cabinets/${r}`,{method:"DELETE"})).ok?(t(e.filter(e=>e.id!==r)),c("تم حذف الخزانة بنجاح"),setTimeout(()=>c(""),3e3)):c("خطأ في حذف الخزانة")}catch(e){console.error("Error deleting cabinet:",e),c("خطأ في حذف الخزانة")}};return l?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-secondary-500"})}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة الخزانات"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"إضافة وتعديل وحذف الخزانات"})]}),(0,s.jsxs)("button",{className:"bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-6 py-3 rounded-lg font-medium hover:from-secondary-600 hover:to-secondary-700 transition-all duration-300 flex items-center gap-2",children:[(0,s.jsx)("span",{children:"➕"}),"إضافة خزانة جديدة"]})]}),d&&(0,s.jsx)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`p-4 rounded-lg ${d.includes("نجاح")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:d}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-secondary-600",children:e.length}),(0,s.jsx)("div",{className:"text-gray-600",children:"إجمالي الخزانات"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.reduce((e,t)=>e+(t.images?.length||0),0)}),(0,s.jsx)("div",{className:"text-gray-600",children:"إجمالي الصور"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:r.length}),(0,s.jsx)("div",{className:"text-gray-600",children:"الفئات المتاحة"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[(0,s.jsxs)("div",{className:"h-48 bg-gray-200 relative",children:[e.images&&e.images.length>0?(0,s.jsx)("img",{src:e.images[0].image_url,alt:e.title,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-500",children:"لا توجد صور"}),e.category_name&&(0,s.jsx)("div",{className:"absolute top-2 right-2 bg-secondary-500 text-white px-2 py-1 rounded text-sm",children:e.category_name})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,s.jsxs)("span",{children:[e.images?.length||0," صورة"]}),(0,s.jsxs)("span",{children:["ID: ",e.id]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{className:"flex-1 bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 transition-colors text-sm",children:"تعديل"}),(0,s.jsx)("button",{onClick:()=>m(e.id),className:"flex-1 bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 transition-colors text-sm",children:"حذف"})]})]})]},e.id))}),0===e.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDDC4️"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:"لا توجد خزانات"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"ابدأ بإضافة أول خزانة إلى المعرض"}),(0,s.jsx)("button",{className:"bg-secondary-500 text-white px-6 py-3 rounded-lg hover:bg-secondary-600 transition-colors",children:"إضافة خزانة جديدة"})]})]})},g=()=>{let[e,t]=(0,a.useState)({socialMedia:[],quickLinks:[],contactInfo:[],settings:{copyright_text:""}}),[r,i]=(0,a.useState)(!0),[l,n]=(0,a.useState)(!1),[d,c]=(0,a.useState)("");(0,a.useEffect)(()=>{x()},[]);let x=async()=>{try{let e=await fetch("/api/footer");if(e.ok){let r=await e.json();t(r)}}catch(e){console.error("Error fetching footer data:",e),c("خطأ في تحميل البيانات")}finally{i(!1)}},m=async()=>{n(!0),c("");try{(await fetch("/api/footer",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?(c("تم حفظ البيانات بنجاح"),setTimeout(()=>c(""),3e3)):c("خطأ في حفظ البيانات")}catch(e){console.error("Error saving footer data:",e),c("خطأ في حفظ البيانات")}finally{n(!1)}},h=(r,s,a)=>{let i=[...e.socialMedia];i[r]={...i[r],[s]:a},t({...e,socialMedia:i})},g=r=>{let s=e.socialMedia.filter((e,t)=>t!==r);t({...e,socialMedia:s})},p=(r,s,a)=>{let i=[...e.contactInfo];i[r]={...i[r],[s]:a},t({...e,contactInfo:i})},u=r=>{let s=e.contactInfo.filter((e,t)=>t!==r);t({...e,contactInfo:s})};return r?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة التذييل"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"تحكم في معلومات التواصل ووسائل التواصل الاجتماعي"})]}),(0,s.jsx)("button",{onClick:m,disabled:l,className:"bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-300 disabled:opacity-50",children:l?"جاري الحفظ...":"حفظ التغييرات"})]}),d&&(0,s.jsx)(o.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`p-4 rounded-lg ${d.includes("نجاح")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:d}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"وسائل التواصل الاجتماعي"}),(0,s.jsx)("button",{onClick:()=>{t({...e,socialMedia:[...e.socialMedia,{platform:"",url:"",icon:""}]})},className:"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm",children:"إضافة"})]}),(0,s.jsx)("div",{className:"space-y-4",children:e.socialMedia.map((e,t)=>(0,s.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,s.jsx)("input",{type:"text",placeholder:"اسم المنصة",value:e.platform,onChange:e=>h(t,"platform",e.target.value),className:"px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"}),(0,s.jsx)("input",{type:"url",placeholder:"الرابط",value:e.url,onChange:e=>h(t,"url",e.target.value),className:"px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("input",{type:"text",placeholder:"الأيقونة",value:e.icon,onChange:e=>h(t,"icon",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"}),(0,s.jsx)("button",{onClick:()=>g(t),className:"bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600 transition-colors text-sm",children:"حذف"})]})]})},t))})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"معلومات التواصل"}),(0,s.jsx)("button",{onClick:()=>{t({...e,contactInfo:[...e.contactInfo,{icon:"",text:"",type:"other"}]})},className:"bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors text-sm",children:"إضافة"})]}),(0,s.jsx)("div",{className:"space-y-4",children:e.contactInfo.map((e,t)=>(0,s.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,s.jsx)("input",{type:"text",placeholder:"الأيقونة",value:e.icon,onChange:e=>p(t,"icon",e.target.value),className:"px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"}),(0,s.jsx)("input",{type:"text",placeholder:"النص",value:e.text,onChange:e=>p(t,"text",e.target.value),className:"px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("select",{value:e.type,onChange:e=>p(t,"type",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm",children:[(0,s.jsx)("option",{value:"address",children:"عنوان"}),(0,s.jsx)("option",{value:"phone",children:"هاتف"}),(0,s.jsx)("option",{value:"email",children:"بريد إلكتروني"}),(0,s.jsx)("option",{value:"hours",children:"ساعات العمل"}),(0,s.jsx)("option",{value:"other",children:"أخرى"})]}),(0,s.jsx)("button",{onClick:()=>u(t),className:"bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600 transition-colors text-sm",children:"حذف"})]})]})},t))})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"نص حقوق الطبع والنشر"}),(0,s.jsx)("input",{type:"text",value:e.settings.copyright_text,onChange:r=>t({...e,settings:{...e.settings,copyright_text:r.target.value}}),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"\xa9 2024 عجائب الخبراء. جميع الحقوق محفوظة."})]})]})},p=({onLogout:e})=>{let[t,r]=(0,a.useState)("dashboard"),[i,n]=(0,a.useState)(!1);return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[(0,s.jsx)(l,{activeSection:t,onSectionChange:r,isOpen:i,onToggle:()=>n(!i)}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col lg:mr-64",children:[(0,s.jsx)(d,{onLogout:e,onMenuToggle:()=>n(!i)}),(0,s.jsx)("main",{className:"flex-1 p-6",children:(0,s.jsx)("div",{className:"animate-fade-in",children:(()=>{switch(t){case"dashboard":default:return(0,s.jsx)(c,{});case"hero":return(0,s.jsx)(x,{});case"kitchens":return(0,s.jsx)(m,{});case"cabinets":return(0,s.jsx)(h,{});case"footer":return(0,s.jsx)(g,{})}})()},t)})]}),i&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>n(!1)})]})},u=()=>{let[e,t]=(0,a.useState)(!1),[r,l]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{(()=>{let e=localStorage.getItem("admin_token"),r=localStorage.getItem("admin_token_expiry");e&&r&&(new Date().getTime()<parseInt(r)?t(!0):(localStorage.removeItem("admin_token"),localStorage.removeItem("admin_token_expiry"))),l(!1)})()},[]),r)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 flex items-center justify-center",dir:"rtl",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):(0,s.jsx)("div",{className:"min-h-screen",dir:"rtl",children:e?(0,s.jsx)(p,{onLogout:()=>{t(!1),localStorage.removeItem("admin_token"),localStorage.removeItem("admin_token_expiry")}}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30",children:(0,s.jsx)(i,{onLogin:e=>{if(e){t(!0);let e=new Date().getTime()+864e5;localStorage.setItem("admin_token","authenticated"),localStorage.setItem("admin_token_expiry",e.toString())}}})})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5172:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"/var/www/html/src/app/admin/page.tsx"]}]},{metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/var/www/html/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["/var/www/html/src/app/admin/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5713:(e,t,r)=>{Promise.resolve().then(r.bind(r,1046))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,425,866,429],()=>r(5172));module.exports=s})();