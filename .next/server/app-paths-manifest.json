{"/api/cabinets/route": "app/api/cabinets/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/footer/route": "app/api/footer/route.js", "/api/hero/route": "app/api/hero/route.js", "/api/kitchens/route": "app/api/kitchens/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/why-choose-us/route": "app/api/why-choose-us/route.js", "/api/cabinets/[id]/route": "app/api/cabinets/[id]/route.js", "/api/kitchens/[id]/route": "app/api/kitchens/[id]/route.js", "/manifest.webmanifest/route": "app/manifest.webmanifest/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/uploads/route": "app/api/uploads/route.js", "/admin/page": "app/admin/page.js", "/cabinets/page": "app/cabinets/page.js", "/kitchens/page": "app/kitchens/page.js", "/page": "app/page.js", "/privacy/page": "app/privacy/page.js", "/terms/page": "app/terms/page.js", "/about/page": "app/about/page.js", "/contact/page": "app/contact/page.js", "/sitemap-page/page": "app/sitemap-page/page.js"}