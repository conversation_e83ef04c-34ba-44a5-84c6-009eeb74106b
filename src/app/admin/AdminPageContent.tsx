'use client'

import { useState, useEffect } from 'react'
import AdminLogin from '@/components/admin/AdminLogin'
import AdminDashboard from '@/components/admin/AdminDashboard'

const AdminPageContent = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = () => {
      const token = localStorage.getItem('admin_token')
      const expiry = localStorage.getItem('admin_token_expiry')
      
      if (token && expiry) {
        const now = new Date().getTime()
        if (now < parseInt(expiry)) {
          setIsAuthenticated(true)
        } else {
          // Token expired, remove it
          localStorage.removeItem('admin_token')
          localStorage.removeItem('admin_token_expiry')
        }
      }
      setLoading(false)
    }

    checkAuth()
  }, [])

  const handleLogin = (success: boolean) => {
    if (success) {
      setIsAuthenticated(true)
      // Set token with 24 hour expiry
      const expiry = new Date().getTime() + (24 * 60 * 60 * 1000)
      localStorage.setItem('admin_token', 'authenticated')
      localStorage.setItem('admin_token_expiry', expiry.toString())
    }
  }

  const handleLogout = () => {
    setIsAuthenticated(false)
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_token_expiry')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 flex items-center justify-center" dir="rtl">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" dir="rtl">
      {isAuthenticated ? (
        <AdminDashboard onLogout={handleLogout} />
      ) : (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
          <AdminLogin onLogin={handleLogin} />
        </div>
      )}
    </div>
  )
}

export default AdminPageContent
