// PM2 Ecosystem Configuration for Expert Wonders (Next.js)
// ملف إعداد PM2 لعجائب الخبراء - Next.js

module.exports = {
  apps: [
    {
      name: 'expert-wonders-nextjs',
      script: '.next/standalone/server.js',
      cwd: '/var/www/html',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOSTNAME: '0.0.0.0'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOSTNAME: '0.0.0.0'
      },
      log_file: '/root/.pm2/logs/expert-wonders-nextjs.log',
      out_file: '/root/.pm2/logs/expert-wonders-nextjs-out.log',
      error_file: '/root/.pm2/logs/expert-wonders-nextjs-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs', '.next'],
      autorestart: true
    },
    {
      name: 'expert-wonders-api-legacy',
      script: 'server.js',
      cwd: './api',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      log_file: '/root/.pm2/logs/expert-wonders-api-legacy.log',
      out_file: '/root/.pm2/logs/expert-wonders-api-legacy-out.log',
      error_file: '/root/.pm2/logs/expert-wonders-api-legacy-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      autorestart: true
    }
  ],

  deploy: {
    production: {
      user: 'root',
      host: '************',
      ref: 'origin/main',
      repo: '**************:your-repo/expert-wonders-nextjs.git',
      path: '/var/www/html',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.cjs --env production',
      'pre-setup': ''
    }
  }
};
